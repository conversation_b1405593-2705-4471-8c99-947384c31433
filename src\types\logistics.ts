// 场地使用申请详情接口
export interface SiteUseApplyDetail {
  id: number
  jxrwid: number
  zc: number
  skrq: string // 使用日期
  skkssj: string
  skjssj: string
  xqs: number // 星期
  jc: string // 节次
  jcshow: string
  sknl: string // 使用主题
  skfs: string
  skfsmc: string
  zyts: number
  zypgfs: string
  zypgfsmc: string
  fzrszs: string
  syyqsbsl: string
  skcdlx: string
  skcddm: string
  skcdmc: string // 使用场地
  skjs: string
  skjsxm: string // 负责人
  skbj: string
  skbjmc: string
  syrs: number
  remark: string // 备注
  create_time: number
  update_time: number
  deltag: number
  oprybh: string
  ks: number
  xz: number
  glid: number
  skjhzxzt: number
  skjhgzlrdzt: number
  zynr: string
  skjhjsqrzt: number
  skjhjsqrsj: string
  skjhjsqrrybh: string
  skjhjsqrryxm: string
  skjhxsqrzt: number
  skjhxsqrsj: string
  skjhxsqrxsxh: string
  skjhxsqrxsxm: string
  cdsbzt: number
  cdsbsyqk: string
  spzt: number // 状态
  wljxglid: number
  xsfzid: number
  xsfzmc: string
  fjlb: string
  kqzp: string
  ttbksqzt: number
  csqrshzt: number
  zyfjlb: string
  zbptdm: string
  zbptmc: string
  zbptnr: string
  jxptdm: string
  jxptmc: string
  jxptnr: string
  xsskshzt: number
  lb: number
  lx: string
  ssxq: string
  ssxqmc: string
  ssjzl: string
  ssjzlmc: string
}

// 场地使用申请详情列表请求参数
export interface SiteUseApplyDetailListParams {
  page: number
  pageSize: number
  pid: number
  spzt: number
}

// 场地使用申请详情列表响应
export interface SiteUseApplyDetailListResponse {
  items: SiteUseApplyDetail[]
  query: Record<string, any>
  total: number
  id: number
}
