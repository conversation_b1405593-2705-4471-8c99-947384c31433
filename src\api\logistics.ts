import request from '@/utils/request'
import type {
  SiteUseApplyDetailListParams,
  SiteUseApplyDetailListResponse,
} from '@/types/logistics'

/**
 * 获取场地使用申请详情列表
 * @param params 请求参数
 * @returns 场地使用申请详情列表
 */
export const getSiteUseApplyDetailList = (params: SiteUseApplyDetailListParams) => {
  return request<SiteUseApplyDetailListResponse>('/siteUseApply/detailList', {
    method: 'POST',
    data: params,
  })
}
